"""
Langchain Image Generation Agent.
"""
from typing import List, Dict, Any
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.agents import create_openai_tools_agent, AgentExecutor
from langchain_core.tools import BaseTool
from src.llm_integration import LLMIntegration
from src.image_generation_tool import OpenAIImageGenerator
from src.image_edit_tool import OpenAIImageEditor
from src.responses_image_tool import ResponsesImageTool
from config.settings import Settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageGenerationAgent:
    """Main agent class that combines LLM reasoning with image generation capabilities."""
    
    def __init__(self):
        """Initialize the image generation agent."""
        logger.info("Initializing Image Generation Agent...")

        # Initialize custom ruleset support
        self.custom_ruleset = None
        self.default_system_prompt = self._get_default_system_prompt()

        # Initialize components
        self.llm_integration = LLMIntegration()
        self.image_generator = OpenAIImageGenerator()
        self.image_editor = OpenAIImageEditor()
        self.responses_tool = ResponsesImageTool()  # Add the new primary tool

        # Create the agent prompt
        self.prompt = self._create_agent_prompt()

        # Create tools list with user-controlled selection via API mode
        self.tools = [self.responses_tool, self.image_generator, self.image_editor]

        # Bind tools to the LLM
        self.llm_with_tools = self.llm_integration.llm.bind_tools(self.tools)

        # Create the agent
        self.agent = create_openai_tools_agent(
            llm=self.llm_integration.llm,
            tools=self.tools,
            prompt=self.prompt
        )

        # Create agent executor
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=3
        )

        logger.info("Image Generation Agent initialized successfully")
    
    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt."""
        return """You are an advanced AI assistant specialized in image generation and editing using OpenAI's cutting-edge Responses API technology.

🎯 DYNAMIC TOOL SELECTION: User-Controlled API Mode
You have access to both advanced and legacy image generation tools. The user controls which API mode to use through their interface.

⚠️ CRITICAL RULES FOR UPLOADED IMAGES:
- When users upload images and ask to modify them, DO NOT analyze the images first
- Use ResponsesImageTool with ONLY the prompt parameter (no reference_images parameter)
- DO NOT pass reference_images parameter when uploaded images are available
- The uploaded images are automatically used by the tool

🔧 AVAILABLE TOOLS:
1. **ResponsesImageTool** (ADVANCED) - Multi-turn image generation with automatic prompt enhancement
2. **OpenAIImageGenerator** (LEGACY) - Direct image generation with exact prompt preservation
3. **OpenAIImageEditor** (LEGACY) - Image editing with base64 input and exact prompt preservation

📋 TOOL SELECTION RULES:

WHEN USER SPECIFIES "Use ResponsesImageTool":
✅ Use ResponsesImageTool for all image generation requests
✅ Leverage multi-turn editing, streaming, and reference image features
✅ Automatic prompt optimization will occur (can be displayed if show_revised_prompt=true)
✅ Advanced conversation and editing capabilities available

WHEN USER SPECIFIES "Use OpenAIImageGenerator or OpenAIImageEditor tools only":
✅ Use OpenAIImageGenerator for new image generation
✅ Use OpenAIImageEditor for image editing tasks
✅ NO automatic prompt enhancement - exact prompt preservation
✅ Clean, straightforward output without AI optimization
✅ Legacy mode for users who prefer direct control

🎨 MULTI-TURN DETECTION:
Automatically set continue_previous=True when users say:
- "continue [instruction]"
- "edit the previous image"
- "modify the last image"
- "make it [different]" (referring to previous generation)
- "change the [aspect]" (referring to previous generation)
- "now make it [variation]"
- "improve it by [instruction]"

📎 UPLOADED IMAGE HANDLING:
When users upload images and ask to modify/edit them:
✅ DIRECTLY use ResponsesImageTool with ONLY the prompt parameter
✅ DO NOT pass reference_images parameter - uploaded images are used automatically
✅ DO NOT try to analyze the image first - go straight to modification
✅ Examples: ResponsesImageTool(prompt="make this brighter"), ResponsesImageTool(prompt="add text to this image")

📎 REFERENCE IMAGE DETECTION:
Use reference_images parameter when users:
- Provide file paths: "use image.jpg as reference"
- Mention style transfer: "make it look like this style"
- Want composition guidance: "based on this layout"
- Say: "using these images", "reference these", "style like"

🎬 STREAMING DETECTION:
Set stream_partial=True when users:
- Want to see progress: "show me the generation process"
- Say: "stream the generation", "show partial results"
- Request: "let me see it being created"

💡 ENHANCED CAPABILITIES EXPLANATION:
When users ask about your capabilities, explain:

1. **Multi-turn Image Editing**: "I can iteratively refine images through conversation. Generate an image, then ask me to modify specific aspects like 'make it brighter' or 'add more details to the background'."

2. **Reference Image Support**: "You can provide reference images by giving me file paths. I'll use them to guide style, composition, or content. Just say 'use image.jpg as reference' or 'style it like photo.png'."

3. **Streaming Generation**: "I can show you partial images as they're being created. Ask me to 'stream the generation' to see the creative process unfold."

4. **Manual Prompt Enhancement**: "You can use the 'Enhance Prompt' button in the interface to improve your prompts with AI assistance when desired."

🔄 CONVERSATION FLOW EXAMPLES:

Example 1 - Responses API Mode (Default):
User: "Generate a sunset landscape [Use ResponsesImageTool for advanced features]"
You: Use ResponsesImageTool with prompt="Generate a sunset landscape"

Example 2 - Legacy API Mode:
User: "Generate a sunset landscape [Use OpenAIImageGenerator or OpenAIImageEditor tools only - no automatic prompt enhancement]"
You: Use OpenAIImageGenerator with prompt="Generate a sunset landscape"

Example 3 - Multi-turn with Responses API:
User: "Generate a sunset landscape [Use ResponsesImageTool for advanced features]"
You: Use ResponsesImageTool with prompt="Generate a sunset landscape"
User: "Make it more dramatic [Use ResponsesImageTool for advanced features]"
You: Use ResponsesImageTool with continue_previous=True and prompt="Make it more dramatic"

Example 4 - Show revised prompt:
User: "Create a sunset landscape [Use ResponsesImageTool for advanced features] [Additional parameters: show_revised_prompt=true]"
You: Use ResponsesImageTool with prompt="Create a sunset landscape" and show_revised_prompt=True

🚨 IMPORTANT GUIDELINES:

1. **Always prefer ResponsesImageTool** unless there's a specific technical reason to use legacy tools
2. **Automatically detect multi-turn scenarios** - don't ask users to specify continue_previous
3. **Proactively suggest reference images** when users describe wanting specific styles
4. **Explain the enhanced capabilities** when users seem unaware of new features
5. **Handle file paths gracefully** - validate and guide users on proper image formats
6. **Maintain conversation context** - remember previous generations for seamless multi-turn editing
7. **Handle show_revised_prompt parameter** - when users include "show_revised_prompt=true" in their parameters, pass this to ResponsesImageTool to display the AI-optimized prompt

🎯 SUCCESS METRICS:
- Prioritize user experience with advanced features
- Minimize tool switching and maximize ResponsesImageTool usage
- Provide clear feedback about which capabilities are being used
- Guide users toward discovering new features naturally

Remember: You're not just generating images, you're providing an advanced creative workflow with iterative refinement, style guidance, and real-time feedback capabilities."""

    def _get_current_system_prompt(self) -> str:
        """Get the current system prompt, including custom ruleset if set."""
        base_prompt = self.default_system_prompt

        if self.custom_ruleset:
            return f"""{base_prompt}

Additionally, you must follow these custom rules:
{self.custom_ruleset}

These custom rules take precedence over default behaviors when there's a conflict."""

        return base_prompt

    def _create_agent_prompt(self) -> ChatPromptTemplate:
        """
        Create enhanced system prompt with Responses API capabilities.

        Returns:
            ChatPromptTemplate: The prompt template for the agent with new tool priorities
        """
        system_message = self._get_current_system_prompt()

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_message),
            MessagesPlaceholder("chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder("agent_scratchpad")
        ])

        return prompt

    def set_custom_ruleset(self, ruleset: str):
        """Set a custom ruleset for the agent."""
        self.custom_ruleset = ruleset
        # Re-initialize the agent chain with new system prompt
        self._reinitialize_with_prompt(self._get_current_system_prompt())

    def clear_custom_ruleset(self):
        """Clear the custom ruleset and revert to default behavior."""
        self.custom_ruleset = None
        self._reinitialize_with_prompt(self.default_system_prompt)

    def ensure_custom_ruleset(self, ruleset: str):
        """Ensure the custom ruleset is applied if different from current."""
        if self.custom_ruleset != ruleset:
            self.set_custom_ruleset(ruleset)

    def _reinitialize_with_prompt(self, system_prompt: str):
        """Reinitialize the agent with a new system prompt."""
        # Update the prompt in the existing agent setup
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder("chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder("agent_scratchpad")
        ])

        # Recreate the agent with the new prompt
        self.agent = create_openai_tools_agent(
            llm=self.llm_integration.llm,
            tools=self.tools,
            prompt=self.prompt
        )

        # Update the executor
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=3
        )

    def invoke(self, user_input: str, chat_history: List[BaseMessage] = None, processed_images: dict = None) -> str:
        """
        Process user input and generate a response.

        Args:
            user_input: The user's message/request
            chat_history: Optional list of previous messages
            processed_images: Optional dict with processed image data from ImageEncoder

        Returns:
            str: The agent's response
        """
        try:
            logger.info(f"Processing user input: '{user_input[:50]}...'")

            # Enhance input with image information if available
            enhanced_input = user_input
            if processed_images and processed_images.get('success_count', 0) > 0:
                # Set processed images on tools so they can access them
                self.image_editor.set_processed_images(processed_images)
                self.responses_tool.set_processed_images(processed_images)

                # Add context about available images for the LLM
                image_count = processed_images['success_count']
                enhanced_input += f"\n\nNote: {image_count} reference image(s) have been uploaded and are available. You can:\n1. Use ResponsesImageTool to generate new images using these as style/content references\n2. Use ResponsesImageTool to modify/edit these uploaded images with new prompts\n3. Use OpenAIImageEditor for direct image editing\n\nThe uploaded images will automatically be used as reference images by the ResponsesImageTool."
                logger.info(f"Enhanced input with {image_count} processed images and updated tools")

            # Prepare input for the agent
            agent_input = {
                "input": enhanced_input,
                "chat_history": chat_history or [],
                "processed_images": processed_images  # Pass to tools if needed
            }
            
            # Invoke the agent
            result = self.agent_executor.invoke(agent_input)
            
            # Extract the output
            response = result.get("output", "I'm sorry, I couldn't process that request.")
            
            logger.info("Successfully processed user input")
            return response
            
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)
            return f"I apologize, but I encountered an error: {error_msg}"
    
    def stream_response(self, user_input: str, chat_history: List[BaseMessage] = None):
        """
        Stream the agent's response (for future implementation).
        
        Args:
            user_input: The user's message/request
            chat_history: Optional list of previous messages
            
        Yields:
            str: Chunks of the agent's response
        """
        # For now, return the full response
        # In the future, this could be implemented with streaming
        response = self.invoke(user_input, chat_history)
        yield response
    
    def test_agent(self) -> bool:
        """
        Test the agent with a simple interaction.
        
        Returns:
            bool: True if test successful, False otherwise
        """
        try:
            # Test basic conversation
            response1 = self.invoke("Hello! How are you?")
            if not response1:
                return False
            
            # Test image generation
            response2 = self.invoke("Please generate an image of a sunset over a mountain lake")
            if not response2 or "Error" in response2:
                return False
            
            logger.info("Agent test passed")
            return True
            
        except Exception as e:
            logger.error(f"Agent test failed: {str(e)}")
            return False

# Test function for standalone usage
def test_agent():
    """Test the complete agent functionality."""
    try:
        agent = ImageGenerationAgent()
        
        if agent.test_agent():
            print("✓ Agent test passed")
            return True
        else:
            print("✗ Agent test failed")
            return False
    except Exception as e:
        print(f"✗ Agent test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    test_agent()
