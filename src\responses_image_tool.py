"""
Advanced Langchain tool for image generation using OpenAI's Responses API.
Provides multi-turn editing, reference image support, and streaming capabilities.
"""
from typing import Optional, Type, List, Dict, Any, Union
from pydantic import BaseModel, Field
from langchain_core.tools import BaseTool
from langchain_core.callbacks import CallbackManagerForToolRun
import openai
from config.settings import Settings
from utils.image_encoder import ImageEncoder
import logging
import base64
import os
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResponsesImageInput(BaseModel):
    """Input schema for the Responses API image generation tool."""
    prompt: str = Field(description="Detailed text prompt describing the image to generate or edit")
    reference_images: Optional[List[str]] = Field(
        default=None,
        description="List of file paths to reference images for style/content guidance"
    )
    continue_previous: Optional[bool] = Field(
        default=False,
        description="Whether to continue from the previous generation (multi-turn editing)"
    )
    stream_partial: Optional[bool] = Field(
        default=False,
        description="Whether to stream partial images during generation"
    )
    quality: Optional[str] = Field(
        default="high",
        description="Image quality: 'low', 'medium', 'high'"
    )
    size: Optional[str] = Field(
        default="1024x1024",
        description="Image size: '1024x1024', '1536x1024', '1024x1536'"
    )
    background: Optional[str] = Field(
        default="opaque",
        description="Background type: 'opaque' or 'transparent'"
    )
    input_fidelity: Optional[str] = Field(
        default="low",
        description="Input fidelity for reference images: 'low' or 'high'"
    )
    moderation: Optional[str] = Field(
        default="auto",
        description="Content moderation level: 'auto' or 'low'"
    )
    show_revised_prompt: Optional[bool] = Field(
        default=False,
        description="Whether to display the AI-optimized prompt from the API"
    )

class ResponsesImageTool(BaseTool):
    """
    Advanced image generation tool using OpenAI's Responses API.

    Features:
    - Multi-turn conversation support with previous_response_id
    - Reference image integration with validation
    - Streaming partial image generation
    - Enhanced error handling and logging
    - Conversation history tracking
    """

    name: str = "ResponsesImageTool"
    description: str = (
        "Advanced image generation tool using OpenAI's Responses API. "
        "Supports multi-turn editing, reference images, and streaming generation. "
        "Primary tool for all image generation tasks with enhanced capabilities. "
        "Can automatically use uploaded reference images when available."
    )
    args_schema: Type[BaseModel] = ResponsesImageInput

    def __init__(self):
        """Initialize the Responses API image generation tool."""
        super().__init__()
        # Use private attributes to avoid Pydantic validation
        self._last_response_id: Optional[str] = None
        self._response_history: List[Dict[str, Any]] = []
        self._conversation_context: List[Dict[str, Any]] = []
        self._processed_images = None
        logger.info("Initialized ResponsesImageTool with OpenAI Responses API")

    @property
    def last_response_id(self):
        """Get last response ID."""
        return self._last_response_id

    @last_response_id.setter
    def last_response_id(self, value):
        """Set last response ID."""
        self._last_response_id = value

    @property
    def response_history(self):
        """Get response history."""
        return self._response_history

    @property
    def conversation_context(self):
        """Get conversation context."""
        return self._conversation_context

    @property
    def processed_images(self):
        """Get processed images."""
        return self._processed_images

    @processed_images.setter
    def processed_images(self, value):
        """Set processed images."""
        self._processed_images = value

    def set_processed_images(self, processed_images: dict):
        """Set processed images from uploads for use in generation."""
        self.processed_images = processed_images
        if processed_images and processed_images.get('success_count', 0) > 0:
            logger.info(f"Responses tool now has access to {processed_images['success_count']} processed images")

    def _get_client(self) -> openai.OpenAI:
        """Get the OpenAI client instance."""
        return openai.OpenAI(api_key=Settings.OPENAI_API_KEY)
    
    def _validate_and_prepare_references(self, reference_paths: List[str]) -> List[Dict[str, str]]:
        """
        Validate and prepare reference images for the API.
        
        Args:
            reference_paths: List of file paths to reference images
            
        Returns:
            List of formatted image dictionaries for API consumption
            
        Raises:
            ValueError: If reference image validation fails
        """
        if not reference_paths:
            return []
        
        try:
            # Use ImageEncoder to prepare reference images
            reference_images = ImageEncoder.prepare_reference_images(
                reference_paths, 
                max_images=4  # API limit
            )
            
            logger.info(f"Successfully prepared {len(reference_images)} reference images")
            return reference_images
            
        except Exception as e:
            error_msg = f"Failed to prepare reference images: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
    
    def _build_conversation_input(self, prompt: str, reference_images: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        Build conversation input for the Responses API.

        Args:
            prompt: Text prompt for generation
            reference_images: Prepared reference image data

        Returns:
            Formatted conversation input for the API
        """
        # Enhance prompt when reference images are provided
        enhanced_prompt = prompt
        if reference_images:
            # Add context about reference images to help the model understand them
            ref_count = len(reference_images)
            if ref_count == 1:
                enhanced_prompt = f"{prompt}\n\nI have provided 1 reference image. Please use this image as a reference for style, composition, or content as appropriate for the request."
            else:
                enhanced_prompt = f"{prompt}\n\nI have provided {ref_count} reference images. Please use these images as references for style, composition, or content as appropriate for the request."

            logger.info(f"Enhanced prompt with reference image context: {ref_count} image(s)")

        content = [{"type": "input_text", "text": enhanced_prompt}]

        # Add reference images to content
        for ref_img in reference_images:
            content.append(ref_img)

        return [{"role": "user", "content": content}]
    
    def _save_generated_image(self, base64_data: str, prompt: str) -> str:
        """
        Save generated image to file.
        
        Args:
            base64_data: Base64 encoded image data
            prompt: Original prompt for filename generation
            
        Returns:
            Path to saved image file
        """
        try:
            # Create output directory
            output_dir = "generated_images"
            os.makedirs(output_dir, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Clean prompt for filename (first 30 chars, alphanumeric only)
            clean_prompt = "".join(c for c in prompt[:30] if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_prompt = clean_prompt.replace(' ', '_')
            
            filename = f"responses_api_{timestamp}_{clean_prompt}.png"
            filepath = os.path.join(output_dir, filename)
            
            # Decode and save
            image_bytes = base64.b64decode(base64_data)
            with open(filepath, 'wb') as f:
                f.write(image_bytes)
            
            return os.path.abspath(filepath)
            
        except Exception as e:
            logger.error(f"Failed to save image: {str(e)}")
            return None
    
    def _handle_streaming_response(self, stream, prompt: str) -> str:
        """
        Handle streaming response from the Responses API.
        
        Args:
            stream: Streaming response object
            prompt: Original prompt for context
            
        Returns:
            Success message with final image information
        """
        partial_count = 0
        final_image_data = None
        saved_partials = []
        
        try:
            logger.info("Processing streaming response...")
            
            for event in stream:
                if event.type == "response.image_generation_call.partial_image":
                    partial_count += 1
                    partial_idx = event.partial_image_index
                    partial_b64 = event.partial_image_b64
                    
                    # Save partial image
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    partial_filename = f"partial_{timestamp}_{partial_idx}.png"
                    partial_path = os.path.join("generated_images", partial_filename)
                    
                    os.makedirs("generated_images", exist_ok=True)
                    with open(partial_path, 'wb') as f:
                        f.write(base64.b64decode(partial_b64))
                    
                    saved_partials.append(os.path.abspath(partial_path))
                    logger.info(f"Saved partial image {partial_idx}: {partial_filename}")
                
                elif event.type == "response.image_generation_call.done":
                    # Extract final image data
                    if hasattr(event, 'image_generation_call') and event.image_generation_call:
                        final_image_data = event.image_generation_call.result
                    
                elif event.type == "response.done":
                    # Response completed
                    if hasattr(event, 'response'):
                        self.last_response_id = event.response.id
                        
                        # Extract final image from response output
                        for output in event.response.output:
                            if output.type == "image_generation_call":
                                final_image_data = output.result
                                break
            
            # Save final image
            final_path = None
            if final_image_data:
                final_path = self._save_generated_image(final_image_data, prompt)
            
            # Build result message
            result_parts = [
                f"🎬 Streaming generation completed!",
                f"📝 Prompt: {prompt}",
                f"🎞️  Generated {partial_count} partial image(s)"
            ]
            
            if saved_partials:
                result_parts.append(f"📂 Partial images saved:")
                for i, path in enumerate(saved_partials, 1):
                    result_parts.append(f"   {i}. {os.path.basename(path)}")
            
            if final_path:
                result_parts.append(f"✅ Final image saved: {os.path.basename(final_path)}")
                result_parts.append(f"📁 Full path: {final_path}")
            
            if self.last_response_id:
                result_parts.append(f"🔗 Response ID: {self.last_response_id[:8]}...")
            
            return "\n".join(result_parts)
            
        except Exception as e:
            error_msg = f"Error processing streaming response: {str(e)}"
            logger.error(error_msg)
            return f"❌ Streaming error: {error_msg}"
    
    def _handle_standard_response(self, response, prompt: str, show_revised_prompt: bool = False) -> str:
        """
        Handle standard (non-streaming) response from the Responses API.

        Args:
            response: Response object from the API
            prompt: Original prompt for context

        Returns:
            Success message with image information
        """
        try:
            # Store response ID for multi-turn conversations
            self.last_response_id = response.id

            # Enhanced debugging: Log the full response structure
            logger.info(f"Response ID: {response.id}")
            logger.info(f"Response output count: {len(response.output) if hasattr(response, 'output') else 'No output attr'}")

            # Extract image data from response
            image_data = None
            revised_prompt = None

            # Debug: Log each output item
            for i, output in enumerate(response.output):
                logger.info(f"Output {i}: type='{output.type}', has_result={hasattr(output, 'result')}")
                if output.type == "image_generation_call":
                    if hasattr(output, 'result'):
                        image_data = output.result
                        logger.info(f"Found image data: {len(image_data) if image_data else 0} bytes")
                    else:
                        logger.warning(f"Image generation call found but no result attribute")

                    # Check for revised prompt
                    if hasattr(output, 'revised_prompt'):
                        revised_prompt = output.revised_prompt
                        logger.info(f"Found revised prompt: {revised_prompt[:50]}...")
                    break

            if not image_data:
                # Enhanced error reporting
                output_types = [output.type for output in response.output] if hasattr(response, 'output') else []
                logger.error(f"No image data found. Output types: {output_types}")
                return f"❌ No image data found in response. Output types: {output_types}"
            
            # Save the image
            saved_path = self._save_generated_image(image_data, prompt)
            
            # Build result message
            result_parts = [
                f"✅ Image generated successfully with Responses API!",
                f"📝 Original prompt: {prompt}"
            ]
            
            # Handle revised prompt display based on user preference
            if revised_prompt and revised_prompt != prompt:
                logger.info(f"API provided revised prompt: {revised_prompt}")
                if show_revised_prompt:
                    result_parts.append(f"🔄 AI-optimized prompt: {revised_prompt}")
                # Store for debugging but don't display automatically unless requested
            
            if saved_path:
                result_parts.append(f"💾 Saved to: {os.path.basename(saved_path)}")
                result_parts.append(f"📁 Full path: {saved_path}")
            
            result_parts.append(f"🔗 Response ID: {self.last_response_id[:8]}... (for multi-turn editing)")
            
            # Add to conversation history
            self.response_history.append({
                'id': self.last_response_id,
                'prompt': prompt,
                'revised_prompt': revised_prompt,
                'timestamp': datetime.now().isoformat(),
                'is_continuation': False,
                'saved_path': saved_path
            })
            
            return "\n".join(result_parts)
            
        except Exception as e:
            error_msg = f"Error processing response: {str(e)}"
            logger.error(error_msg)
            return f"❌ Response processing error: {error_msg}"
    
    def _run(
        self,
        prompt: str,
        reference_images: Optional[List[str]] = None,
        continue_previous: Optional[bool] = False,
        stream_partial: Optional[bool] = False,
        quality: Optional[str] = "high",
        size: Optional[str] = "1024x1024",
        background: Optional[str] = "opaque",
        input_fidelity: Optional[str] = "low",
        moderation: Optional[str] = "auto",
        show_revised_prompt: Optional[bool] = False,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """
        Execute the Responses API image generation tool.
        
        Args:
            prompt: Text prompt describing the image
            reference_images: List of reference image file paths
            continue_previous: Whether to continue from previous generation
            stream_partial: Whether to stream partial images
            quality: Image quality setting
            size: Image size setting
            background: Background setting
            input_fidelity: Input fidelity for reference images
            moderation: Content moderation level
            show_revised_prompt: Whether to display the AI-optimized prompt from the API
            run_manager: Callback manager
            
        Returns:
            Success message with image information or error message
        """
        try:
            logger.info(f"Starting Responses API generation: '{prompt[:50]}...'")
            logger.info(f"Parameters - Continue: {continue_previous}, Stream: {stream_partial}, Quality: {quality}")
            
            # Get client
            client = self._get_client()
            
            # Prepare reference images if provided, or use uploaded images
            prepared_references = []
            if reference_images:
                try:
                    prepared_references = self._validate_and_prepare_references(reference_images)
                    logger.info(f"Using {len(prepared_references)} provided reference images")
                except Exception as e:
                    logger.warning(f"Failed to prepare provided reference images: {str(e)}")
            elif self.processed_images and self.processed_images.get('reference_images'):
                # Use uploaded reference images if no specific ones provided
                prepared_references = self.processed_images['reference_images']
                logger.info(f"Using {len(prepared_references)} uploaded reference images")
            
            # Build API parameters
            api_params = {
                "model": "gpt-4o-mini",  # Model that supports image generation tool
                "tools": [{
                    "type": "image_generation",
                    "quality": quality,
                    "size": size,
                    "background": background,
                    "moderation": moderation
                }]
            }
            
            # Add input fidelity for reference images
            if prepared_references and input_fidelity == "high":
                api_params["tools"][0]["input_fidelity"] = "high"
            
            # Add streaming configuration
            if stream_partial:
                api_params["stream"] = True
                api_params["tools"][0]["partial_images"] = 2  # Show 2 partial images
            
            # Handle multi-turn conversation
            if continue_previous and self.last_response_id:
                api_params["previous_response_id"] = self.last_response_id
                api_params["input"] = prompt
                logger.info(f"Continuing from response: {self.last_response_id[:8]}...")
            else:
                # Build conversation input
                conversation_input = self._build_conversation_input(prompt, prepared_references)
                api_params["input"] = conversation_input
            
            # Make API call
            if stream_partial:
                logger.info("Starting streaming generation...")
                stream = client.responses.create(**api_params)
                result = self._handle_streaming_response(stream, prompt)
            else:
                logger.info("Starting standard generation...")
                response = client.responses.create(**api_params)
                result = self._handle_standard_response(response, prompt, show_revised_prompt)
            
            # Update history for continuations
            if continue_previous and self.response_history:
                self.response_history.append({
                    'id': self.last_response_id,
                    'prompt': prompt,
                    'timestamp': datetime.now().isoformat(),
                    'is_continuation': True,
                    'parent_id': self.response_history[-1].get('id')
                })
            
            return result
            
        except openai.OpenAIError as e:
            error_msg = f"OpenAI Responses API error: {str(e)}"
            logger.error(error_msg)
            return f"❌ API Error: {error_msg}"
        
        except Exception as e:
            error_msg = f"Unexpected error in ResponsesImageTool: {str(e)}"
            logger.error(error_msg)
            return f"❌ Tool Error: {error_msg}"
    
    async def _arun(
        self,
        prompt: str,
        reference_images: Optional[List[str]] = None,
        continue_previous: Optional[bool] = False,
        stream_partial: Optional[bool] = False,
        quality: Optional[str] = "high",
        size: Optional[str] = "1024x1024",
        background: Optional[str] = "opaque",
        input_fidelity: Optional[str] = "low",
        moderation: Optional[str] = "auto",
        show_revised_prompt: Optional[bool] = False,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Async version of the tool execution."""
        return self._run(
            prompt, reference_images, continue_previous, stream_partial,
            quality, size, background, input_fidelity, moderation, show_revised_prompt, run_manager
        )
    
    def clear_conversation_history(self):
        """Clear conversation history and reset state."""
        self.last_response_id = None
        self.response_history = []
        self.conversation_context = []
        logger.info("Cleared conversation history")
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get summary of current conversation state."""
        return {
            'last_response_id': self.last_response_id,
            'total_generations': len(self.response_history),
            'has_active_session': self.last_response_id is not None,
            'recent_prompts': [h.get('prompt', '')[:50] for h in self.response_history[-3:]]
        }