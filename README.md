# Langchain Image Generation Agent

A sophisticated AI agent built with LangChain that combines conversational AI capabilities with advanced image generation using OpenAI's latest Responses API and gpt-image-1 model.

## 🚀 Features

- **Conversational AI**: Powered by OpenAI's `gpt-4o-mini` for natural language understanding and reasoning
- **Advanced Image Generation**: Integrated with OpenAI's Responses API and `gpt-image-1` for cutting-edge image creation
- **Multi-turn Image Editing**: Iteratively refine images through conversation with automatic context tracking
- **Reference Image Support**: Use your own images for style and content guidance
- **Streaming Generation**: Watch images being created in real-time with partial previews
- **Smart Tool Selection**: Automatically determines optimal tools based on user requests
- **Interactive Interface**: Enhanced command-line interface with advanced features
- **Comprehensive Testing**: Built-in test suite for all components
- **Memory Management**: Maintains conversation context and generation history

## 🏗️ Project Structure

```
Image_Agent/
├── config/
│   ├── __init__.py
│   └── settings.py              # Configuration and environment variables
├── src/
│   ├── __init__.py
│   ├── agent.py                 # Main agent with Responses API integration
│   ├── llm_integration.py       # Language model integration
│   ├── responses_image_tool.py  # PRIMARY: Advanced Responses API tool
│   ├── image_generation_tool.py # LEGACY: Simple image generation
│   └── image_edit_tool.py       # LEGACY: Basic image editing
├── utils/
│   ├── __init__.py
│   └── image_encoder.py         # Image validation and encoding utilities
├── generated_images/            # Auto-created directory for saved images
├── notebooks/                   # Jupyter notebooks for experimentation
├── LLM_docs/                   # OpenAI API documentation
├── main.py                     # Enhanced main application entry point
├── requirements.txt            # Python dependencies
├── docker-compose.yml          # Docker deployment configuration
├── Dockerfile                  # Container configuration
├── .env                       # Environment variables (API keys)
├── .gitignore                 # Git ignore file
├── plan.md                    # Development plan
├── WEB_DEPLOYMENT_GUIDE.md    # Web deployment instructions
└── README.md                  # This file
```

## 🛠️ Installation

### Prerequisites

- Python 3.8 or higher
- OpenAI API key with access to Responses API and gpt-image-1

### Setup

1. **Clone or navigate to the project directory:**
   ```bash
   cd Image_Agent
   ```

2. **Create and activate a virtual environment:**
   ```bash
   python -m venv venv
   
   # On Windows:
   venv\Scripts\activate
   
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**
   - Ensure your `.env` file contains your OpenAI API key:
   ```
   OPENAI_API_KEY=your_api_key_here
   ```

## 🎮 Usage

### Enhanced Interactive Mode

Run the agent in interactive mode for the full conversational experience:

```bash
python main.py
```

### 🚀 New Advanced Commands

- **`refs [path1] [path2]...`** - Load reference images for style/content guidance
- **`continue [instruction]`** - Edit the last generated image with new instructions
- **`stream [prompt]`** - Generate images with streaming partial results
- **`clear-refs`** - Remove all loaded reference images
- **`history`** - Show generation history with response IDs

### 🔧 Standard Commands

- **`clear`** - Clear chat history
- **`test`** - Run system tests
- **`exit/quit`** - End the session

### 💡 Usage Examples

#### Multi-turn Image Editing
```
🧑 You: Generate a sunset landscape
🤖 Agent: [Creates beautiful sunset landscape]

🧑 You: continue make it more dramatic with storm clouds
🤖 Agent: [Enhances the image with dramatic storm clouds]

🧑 You: now add a lighthouse in the distance
🤖 Agent: [Adds lighthouse while maintaining the dramatic scene]
```

#### Reference Image Workflow
```
🧑 You: refs style_reference.jpg logo.png
🤖 Agent: ✅ Successfully loaded 2 reference image(s)

🧑 You: Create a modern website banner using these references
🤖 Agent: [Generates banner incorporating style and logo elements]
```

#### Streaming Generation
```
🧑 You: stream Create a detailed fantasy castle on a floating island
🤖 Agent: 🎬 Starting streaming generation...
         [Shows partial images as they develop]
         ✅ Final image completed!
```

### Single Query Mode

Run a single query from the command line:

```bash
python main.py "Generate an image of a futuristic city at sunset"
```

### Testing

Run the comprehensive test suite:

```bash
python main.py test
```

## 🏛️ Architecture

### 🔧 Core Components

1. **ResponsesImageTool** (`src/responses_image_tool.py`) - **PRIMARY TOOL**
   - OpenAI Responses API integration
   - Multi-turn conversation tracking with `previous_response_id`
   - Streaming partial image generation
   - Reference image support with validation
   - Enhanced error handling and logging

2. **Agent** (`src/agent.py`)
   - Enhanced system prompt with tool prioritization
   - Automatic multi-turn scenario detection
   - Reference image workflow management
   - Intelligent tool selection logic

3. **Image Encoder Utility** (`utils/image_encoder.py`)
   - Image validation and format checking
   - Base64 encoding for API consumption
   - Reference image preparation and optimization
   - File size and format validation

4. **LLM Integration** (`src/llm_integration.py`)
   - OpenAI language model connectivity
   - Model configuration and testing
   - Error handling and validation

5. **Legacy Tools** (Fallback Support)
   - `OpenAIImageGenerator` - Simple image generation
   - `OpenAIImageEditor` - Basic image editing with base64 input

6. **Configuration** (`config/settings.py`)
   - Centralized configuration management
   - Environment variable handling
   - Responses API settings and validation

### 🎯 Tool Selection Logic

The agent automatically selects the optimal tool based on request type:

#### **ResponsesImageTool (PRIMARY)** - Used for:
- ✅ All new image generation requests
- ✅ Multi-turn conversations ("continue", "edit previous", "modify last")
- ✅ Reference image workflows ("style like", "based on", file paths)
- ✅ Streaming generation requests ("show progress", "stream")
- ✅ Complex prompts requiring enhanced AI reasoning

#### **Legacy Tools (FALLBACK)** - Used only when:
- ⚠️ ResponsesImageTool explicitly fails or is unavailable
- ⚠️ User specifically requests "simple generation" or "basic editing"
- ⚠️ Working with existing base64 image data

### 🔄 Multi-turn Conversation Flow

1. **Initial Generation**: User requests image → ResponsesImageTool creates image → Response ID stored
2. **Continuation**: User says "continue [instruction]" → Tool uses `previous_response_id` → Iterative refinement
3. **Context Tracking**: Agent maintains conversation history and automatically detects continuation scenarios

## 🎨 Advanced Capabilities

### Multi-turn Image Editing
- **Automatic Detection**: Agent recognizes continuation phrases
- **Context Preservation**: Maintains visual consistency across edits
- **Iterative Refinement**: Build complex images through conversation

### Reference Image Support
- **Style Transfer**: "Make it look like this style"
- **Composition Guidance**: "Based on this layout"
- **Content Integration**: "Include elements from these images"
- **Format Validation**: Automatic checking of image formats and sizes

### Streaming Generation
- **Real-time Preview**: See images develop progressively
- **Partial Image Saving**: Access intermediate generation stages
- **Progress Feedback**: Visual indicators during generation

### Intelligent Prompting
- **AI Optimization**: Automatic prompt enhancement for better results
- **Revised Prompt Access**: See how AI optimized your request
- **Context Integration**: Smart combination of prompts and references

## 🐳 Docker Deployment

Deploy with Docker for production use:

```bash
# Build and run with Docker Compose
docker-compose up --build

# Access at http://localhost:5000
```

See `WEB_DEPLOYMENT_GUIDE.md` for detailed deployment instructions.

## ⚙️ Configuration

The agent can be configured through `config/settings.py`:

- **Language Model**: Default is `gpt-4o-mini`
- **Image Model**: Default is `gpt-image-1` via Responses API
- **Image Parameters**: Size, quality, background, and moderation settings
- **Tool Behavior**: Prioritization and fallback logic
- **Streaming Settings**: Partial image count and quality

## 🔍 API Models Used

- **Language Model**: `gpt-4o-mini` - OpenAI's efficient reasoning model
- **Image Generation**: `gpt-image-1` via Responses API - Latest multimodal model
- **Legacy Support**: `dall-e-2`, `dall-e-3` - Fallback image models

## 🚨 Troubleshooting

### Common Issues

1. **API Key Error**
   ```
   Error: OPENAI_API_KEY environment variable is required
   ```
   - Solution: Ensure your `.env` file contains a valid OpenAI API key with Responses API access

2. **Reference Image Errors**
   ```
   ❌ Reference image error: Invalid format/size
   ```
   - Solution: Use PNG, JPG, JPEG, or WEBP files under 50MB

3. **Multi-turn Continuation Issues**
   ```
   ❌ No previous response ID available
   ```
   - Solution: Generate an initial image before using continue commands

4. **Streaming Generation Problems**
   ```
   ❌ Streaming error: Connection timeout
   ```
   - Solution: Check internet connectivity and try with shorter prompts

### Debugging

Enable verbose logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

View generation history:
```bash
# In interactive mode
history
```

## 🤝 Contributing

1. Follow the existing code structure and patterns
2. Add comprehensive tests for new features
3. Update documentation for any changes
4. Ensure all tests pass before submitting changes
5. Test both Responses API and legacy tool functionality

## 📄 License

This project is for educational and development purposes. Please ensure compliance with OpenAI's usage policies when using their APIs.

## 🙏 Acknowledgments

- Built with [LangChain](https://python.langchain.com/) framework
- Powered by [OpenAI](https://openai.com/) Responses API and models
- Follows LangChain best practices and patterns
- Implements cutting-edge multi-turn image generation workflows

---

## 🎯 Quick Start Examples

### Basic Generation
```bash
python main.py
🧑 You: Create a serene mountain lake at dawn
```

### Multi-turn Editing
```bash
🧑 You: Generate a cozy coffee shop interior
🤖 Agent: [Creates coffee shop image]
🧑 You: continue add more plants and warm lighting
🤖 Agent: [Enhances with plants and lighting]
```

### Reference-guided Creation
```bash
🧑 You: refs architectural_style.jpg color_palette.png
🧑 You: Design a modern house using these references
```

### Streaming Generation
```bash
🧑 You: stream Create an epic space battle scene
🤖 Agent: 🎬 [Shows progressive development of the scene]
```

Ready to create amazing images with AI? Start with `python main.py` and explore the possibilities! 🚀
